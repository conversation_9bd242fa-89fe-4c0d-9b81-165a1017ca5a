package service

import (
	"bytes"
	"context"
	"fmt"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/log"

	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

type DailyReportService interface {
	GenerateRetentionUntilDate(ctx context.Context, date string) error
	GenerateDailyUserAnalysisForDate(ctx context.Context, date string) error
	ExportDailyUserAnalysis(ctx context.Context, req *v1.ExportDailyUserAnalysisRequest) (*v1.ExportDailyUserAnalysisResponseData, error)
}

type dailyReportService struct {
	anaRepo repository.DailyUserAnalysisRepository
	logger  *log.Logger
}

func (slf *dailyReportService) ExportDailyUserAnalysis(ctx context.Context, req *v1.ExportDailyUserAnalysisRequest) (*v1.ExportDailyUserAnalysisResponseData, error) {
	analyses, err := slf.anaRepo.FetchDailyUserAnalysisByDateRange(ctx, req.AnalysisDate.Start, req.AnalysisDate.End)
	if err != nil {
		return nil, err
	}
	f := excelize.NewFile()
	sheet := "Sheet1"
	f.SetSheetName("Sheet1", sheet)

	headers := []string{
		"日期",
		"新增激活用户数",
		"新增注册用户数",
		"新增注册用户数（包括注册后注销）",
		"企微加粉人数",
		"当日活跃用户数",
		"当前总注册用户数",
		"当前总注册用户数（包括注册后注销）",
		"当日测算总用户数",
		"当日测算总次数",
		"当日人均测算次数",
		"排盘测算人数",
		"排盘测算次数",
		"运势测算人数",
		"运势测算次数",
		"运势页面访问人数",
		"论财测算人数",
		"论财测算次数",
		"论财页面访问人数",
		"合盘测算人数",
		"合盘测算次数",
		"合盘页面访问人数",
		"配饰测算人数",
		"配饰测算次数",
		"配饰页面访问人数",
		"情感测算人数",
		"情感测算次数",
		"情感页面访问人数",
		"高考测算人数",
		"高考测算次数",
		"高考页面访问人数",
		"进入付费页面人数",
		"创建订单人数",
		"支付订单数",
		"支付成功人数",
		"支付成功金额",
		"付费用户arpu",
		"下单率",
		"支付率",
		"新增活跃占比",
		"测算总占比",
		"排盘测算占比",
		"运势测算占比",
		"论财测算占比",
		"合盘测算占比",
		"配饰测算占比",
		"情感测算占比",
		"高考测算占比",
		"次日留存用户数",
		"次留率",
		"3日留存用户数",
		"3日留存率",
		"7日留存用户数",
		"7日留存率",
		"14日留存用户数",
		"14日留存率",
		"次周留存用户数",
		"次周留存率",
		"次月留存用户数",
		"次月留存率",
	}
	for i, h := range headers {
		col, _ := excelize.ColumnNumberToName(i + 1)
		cell := col + "1"
		f.SetCellValue(sheet, cell, h)
	}

	buf := new(bytes.Buffer)
	if err := f.Write(buf); err != nil {
		return nil, err
	}
	return &v1.Media{
		FileName:    fmt.Sprintf("order-%s.xlsx", time.Now().Format("20060102150405")),
		FileBytes:   buf.Bytes(),
		ContentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
	}, nil
}

func NewDailyReportService(anaRepo repository.DailyUserAnalysisRepository, logger *log.Logger) DailyReportService {
	return &dailyReportService{
		anaRepo: anaRepo,
		logger:  logger,
	}
}

func (slf *dailyReportService) setQuery(qry string, m map[string]string) string {
	for k, v := range m {
		qry = strings.ReplaceAll(qry, k, v)
	}
	return qry
}

func (slf *dailyReportService) GenerateRetentionUntilDate(ctx context.Context, date string) error {
	yes, err := time.Parse(time.DateOnly, date)
	if err != nil {
		return err
	}
	start := yes.AddDate(0, 0, -60)
	end := yes.AddDate(0, 0, -1)
	for day := start; !day.After(end); day = day.AddDate(0, 0, 1) {
		// 查询当天的日报
		analysis, err := slf.anaRepo.FetchDailyUserAnalysisByDate(ctx, day.Format("2006-01-02"))
		if err != nil {
			return err
		}
		if analysis == nil {
			slf.logger.Error("日报不存在", zap.String("date", day.Format("2006-01-02")))
			continue
		}
		daysDiff := int(yes.Sub(day).Hours() / 24)
		count := 0
		// 定点留存
		switch daysDiff {
		case 1:
			fmt.Println(day.Format("2006-01-02"), "-> 计算 次留")
			q := slf.setQuery(model.QueryRetentionByDay, map[string]string{
				"PARAM_D": day.Format("2006-01-02"),
				"PARAM_N": "1",
			})
			if err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &count); err != nil {
				slf.logger.Error("计算次留失败", zap.String("date", day.Format("2006-01-02")), zap.Error(err))
			} else {
				analysis.RetentionD1Users = count
				analysis.RetentionD1Rate = float64(count) / float64(analysis.NewRegisteredUsers)
			}
		case 3:
			fmt.Println(day.Format("2006-01-02"), "-> 计算 3日留存")
			q := slf.setQuery(model.QueryRetentionByDay, map[string]string{
				"PARAM_D": day.Format("2006-01-02"),
				"PARAM_N": "3",
			})
			if err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &count); err != nil {
				slf.logger.Error("计算3日留存失败", zap.String("date", day.Format("2006-01-02")), zap.Error(err))
			} else {
				analysis.RetentionD3Users = count
				analysis.RetentionD3Rate = float64(count) / float64(analysis.NewRegisteredUsers)
			}
		case 7:
			fmt.Println(day.Format("2006-01-02"), "-> 计算 7日留存")
			q := slf.setQuery(model.QueryRetentionByDay, map[string]string{
				"PARAM_D": day.Format("2006-01-02"),
				"PARAM_N": "7",
			})
			if err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &count); err != nil {
				slf.logger.Error("计算7日留存失败", zap.String("date", day.Format("2006-01-02")), zap.Error(err))
			} else {
				analysis.RetentionD7Users = count
				analysis.RetentionD7Rate = float64(count) / float64(analysis.NewRegisteredUsers)
			}
		case 14:
			fmt.Println(day.Format("2006-01-02"), "-> 计算 14日留存")
			q := slf.setQuery(model.QueryRetentionByDay, map[string]string{
				"PARAM_D": day.Format("2006-01-02"),
				"PARAM_N": "14",
			})
			if err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &count); err != nil {
				slf.logger.Error("计算14日留存失败", zap.String("date", day.Format("2006-01-02")), zap.Error(err))
			} else {
				analysis.RetentionD14Users = count
				analysis.RetentionD14Rate = float64(count) / float64(analysis.NewRegisteredUsers)
			}
		}
		// 区间留存
		if daysDiff >= 7 && daysDiff <= 14 {
			fmt.Println(day.Format("2006-01-02"), "-> 更新 次周留存")
			q := slf.setQuery(model.QueryRetentionByWeek, map[string]string{
				"PARAM_D": day.Format("2006-01-02"),
			})
			if err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &count); err != nil {
				slf.logger.Error("计算次周留存失败", zap.String("date", day.Format("2006-01-02")), zap.Error(err))
			} else {
				analysis.RetentionWeek2Users = count
				analysis.RetentionWeek2Rate = float64(count) / float64(analysis.NewRegisteredUsers)
			}
		}
		if daysDiff >= 31 && daysDiff <= 60 {
			fmt.Println(day.Format("2006-01-02"), "-> 更新 次月留存")
			q := slf.setQuery(model.QueryRetentionByMonth, map[string]string{
				"PARAM_D": day.Format("2006-01-02"),
			})
			if err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &count); err != nil {
				slf.logger.Error("计算次月留存失败", zap.String("date", day.Format("2006-01-02")), zap.Error(err))
			} else {
				analysis.RetentionMonth2Users = count
				analysis.RetentionMonth2Rate = float64(count) / float64(analysis.NewRegisteredUsers)
			}
		}
		if err = slf.anaRepo.UpdateDailyUserAnalysis(ctx, analysis); err != nil {
			return err
		}
	}
	return nil
}

func (slf *dailyReportService) GenerateDailyUserAnalysisForDate(ctx context.Context, date string) error {
	analysis, err := slf.anaRepo.FetchDailyUserAnalysisByDate(ctx, date)
	if err != nil {
		return err
	}
	if analysis != nil {
		return nil
	}

	var (
		fromAppCollect       model.FromAppCollect
		fromAppUser          model.FromAppUser
		fromUserPaipanRecord model.FromUserPaipanRecord
		fromCalcUsers        model.FromCalcUsers
		fromUserHepanRecord  model.FromUserHepanRecord
		fromUserOrder        model.FromUserOrder
		fromQwContactFollow  model.FromQwContactFollow
		q                    string
	)
	q = slf.setQuery(model.QueryFromAppCollect, map[string]string{
		"PARAM_D": date,
	})
	err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &fromAppCollect)
	if err != nil {
		return err
	}
	q = slf.setQuery(model.QueryFromAppUser, map[string]string{
		"PARAM_D": date,
	})
	err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &fromAppUser)
	if err != nil {
		return err
	}
	q = slf.setQuery(model.QueryFromUserPaipanRecord, map[string]string{
		"PARAM_D": date,
	})
	err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &fromUserPaipanRecord)
	if err != nil {
		return err
	}
	q = slf.setQuery(model.QueryFromCalcUsers, map[string]string{
		"PARAM_D": date,
	})
	err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &fromCalcUsers)
	if err != nil {
		return err
	}
	q = slf.setQuery(model.QueryFromUserHepanRecord, map[string]string{
		"PARAM_D": date,
	})
	err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &fromUserHepanRecord)
	if err != nil {
		return err
	}
	q = slf.setQuery(model.QueryFromUserOrder, map[string]string{
		"PARAM_D": date,
	})
	err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &fromUserOrder)
	if err != nil {
		return err
	}
	q = slf.setQuery(model.QueryFromQwContactFollow, map[string]string{
		"PARAM_D": date,
	})
	err = slf.anaRepo.NewRaw(ctx, q).Scan(ctx, &fromQwContactFollow)
	if err != nil {
		return err
	}

	calcCount := fromUserPaipanRecord.CalcPaipanCount + fromUserPaipanRecord.CalcYunshiCount + fromUserPaipanRecord.CalcLuncaiCount + fromUserPaipanRecord.CalcHepanCount + fromUserPaipanRecord.CalcPeishiCount + fromUserPaipanRecord.CalcGaokaoCount + fromUserPaipanRecord.CalcQingganCount + fromUserHepanRecord.CalcHepanCount
	analysis = &model.DailyUserAnalysis{
		Date:                          date,
		NewActivatedUsers:             fromAppCollect.NewActivatedUsers,
		NewRegisteredUsers:            fromAppUser.NewRegisteredUsers,
		NewRegisteredUsersWithDeleted: fromAppUser.NewRegisteredUsersWithDeleted,
		NewQwContactUsers:             fromQwContactFollow.NewQwContactUsers,
		ActiveUsers:                   fromAppCollect.ActiveUsers,
		TotalUsers:                    fromAppUser.TotalUsers,
		TotalUsersWithDeleted:         fromAppUser.TotalUsersWithDeleted,
		CalcUsers:                     fromCalcUsers.CalcUsers,
		CalcCount:                     calcCount,
		CalcAvgCount:                  float64(calcCount) / float64(fromCalcUsers.CalcUsers),
		CalcPaipanUsers:               fromUserPaipanRecord.CalcPaipanUsers,
		CalcPaipanCount:               fromUserPaipanRecord.CalcPaipanCount,
		CalcYunshiUsers:               fromUserPaipanRecord.CalcYunshiUsers,
		CalcYunshiCount:               fromUserPaipanRecord.CalcYunshiCount,
		PageYunshiUsers:               fromAppCollect.PageYunshiUsers,
		CalcLuncaiUsers:               fromUserPaipanRecord.CalcLuncaiUsers,
		CalcLuncaiCount:               fromUserPaipanRecord.CalcLuncaiCount,
		PageLuncaiUsers:               fromAppCollect.PageLuncaiUsers,
		CalcHepanUsers:                fromUserHepanRecord.CalcHepanUsers,
		CalcHepanCount:                fromUserHepanRecord.CalcHepanCount,
		PageHepanUsers:                fromAppCollect.PageHepanUsers,
		CalcPeishiUsers:               fromUserPaipanRecord.CalcPeishiUsers,
		CalcPeishiCount:               fromUserPaipanRecord.CalcPeishiCount,
		PagePeishiUsers:               fromAppCollect.PagePeishiUsers,
		CalcQingganUsers:              fromUserPaipanRecord.CalcQingganUsers,
		CalcQingganCount:              fromUserPaipanRecord.CalcQingganCount,
		PageQingganUsers:              fromAppCollect.PageQingganUsers,
		CalcGaokaoUsers:               fromUserPaipanRecord.CalcGaokaoUsers,
		CalcGaokaoCount:               fromUserPaipanRecord.CalcGaokaoCount,
		PageGaokaoUsers:               fromAppCollect.PageGaokaoUsers,
		PagePayUsers:                  fromAppCollect.PagePayUsers,
		OrderCreateUsers:              fromUserOrder.OrderCreateUsers,
		OrderPayCount:                 fromUserOrder.OrderPayCount,
		OrderPayUsers:                 fromUserOrder.OrderPayUsers,
		OrderPayAmount:                fromUserOrder.OrderPayAmount,
		PaidUserArpu:                  fromUserOrder.OrderPayAmount / fromUserOrder.OrderPayUsers,
		OrderCreateRate:               float64(fromUserOrder.OrderCreateUsers) / float64(fromAppCollect.PagePayUsers),
		OrderPayRate:                  float64(fromUserOrder.OrderPayUsers) / float64(fromUserOrder.OrderCreateUsers),
		NewerActiveRate:               float64(fromAppCollect.NewActivatedUsers) / float64(fromAppCollect.ActiveUsers),
		CalcRate:                      float64(fromCalcUsers.CalcUsers) / float64(fromAppCollect.ActiveUsers),
		CalcPaipanRate:                float64(fromUserPaipanRecord.CalcPaipanUsers) / float64(fromAppCollect.ActiveUsers),
		CalcYunshiRate:                float64(fromUserPaipanRecord.CalcYunshiUsers) / float64(fromAppCollect.ActiveUsers),
		CalcLuncaiRate:                float64(fromUserPaipanRecord.CalcLuncaiUsers) / float64(fromAppCollect.ActiveUsers),
		CalcHepanRate:                 float64(fromUserHepanRecord.CalcHepanUsers) / float64(fromAppCollect.ActiveUsers),
		CalcPeishiRate:                float64(fromUserPaipanRecord.CalcPeishiUsers) / float64(fromAppCollect.ActiveUsers),
		CalcGaokaoRate:                float64(fromUserPaipanRecord.CalcGaokaoUsers) / float64(fromAppCollect.ActiveUsers),
	}
	id, err := slf.anaRepo.CreateDailyUserAnalysis(ctx, analysis)
	if err != nil {
		return err
	}
	analysis.ID = id
	return nil
}
